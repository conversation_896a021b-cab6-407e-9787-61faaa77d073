package main

import (
	"fmt"
	"time"

	"github.com/teambition/rrule-go"
)

// Helper function to print a slice of time.Time
func printTimeSlice(ts []time.Time) {
	for _, t := range ts {
		fmt.Println(t.Format("2006-01-02 15:04:05 MST")) // Format for better readability
	}
}

// Schedule represents a recurring schedule configuration
type Schedule struct {
	Config ScheduleConfig
}

// ScheduleConfig contains configuration for a schedule
type ScheduleConfig struct {
	RecurringRule string
	StartTime     time.Time
}

// NewTimeToSend calculates the next occurrence after the current time
func (s Schedule) NewTimeToSend(curr time.Time) (time.Time, error) {
	r, err := rrule.StrToRRule(s.Config.RecurringRule)
	if err != nil {
		return time.Time{}, err
	}

	r.OrigOptions.Dtstart = s.Config.StartTime

	return r.After(curr, false), nil
}

func main() {
	fmt.Println("--- Daily RRule for 10 occurrences ---")
	r, err := rrule.NewRRule(rrule.ROption{
		Freq:    rrule.DAILY,
		Count:   10,
		Dtstart: time.Date(1997, 9, 2, 9, 0, 0, 0, time.UTC),
	})
	if err != nil {
		fmt.Println("Error creating RRule:", err)
		return
	}

	fmt.Println("RRule String:", r.String())
	fmt.Println("All occurrences:")
	printTimeSlice(r.All())

	fmt.Println("\n--- Weekly RRule with specific weekdays ---")
	r2, err := rrule.NewRRule(rrule.ROption{
		Freq:    rrule.WEEKLY,
		Count:   5,
		Dtstart: time.Date(2025, 6, 19, 10, 0, 0, 0, time.Local), // Use local time for DTSTART
		Byweekday: []rrule.Weekday{
			rrule.MO, // Monday
			rrule.WE, // Wednesday
			rrule.FR, // Friday
		},
	})
	if err != nil {
		fmt.Println("Error creating RRule 2:", err)
		return
	}
	fmt.Println("RRule String:", r2.String())
	fmt.Println("All occurrences (Mon, Wed, Fri):")
	printTimeSlice(r2.All())

	fmt.Println("\n--- RRuleSet example ---")
	set := rrule.Set{}
	// Add a daily rule
	dailyRule, _ := rrule.NewRRule(rrule.ROption{
		Freq:    rrule.DAILY,
		Count:   5,
		Dtstart: time.Date(2025, 7, 1, 9, 0, 0, 0, time.UTC),
	})
	set.RRule(dailyRule)

	// Add an exclusion date
	set.ExDate(time.Date(2025, 7, 3, 9, 0, 0, 0, time.UTC)) // Exclude July 3rd

	// Add a specific inclusion date
	set.RDate(time.Date(2025, 7, 10, 9, 0, 0, 0, time.UTC)) // Include July 10th

	fmt.Println("RRuleSet String:\n", set.String())
	fmt.Println("All occurrences from RRuleSet:")
	printTimeSlice(set.All())

	fmt.Println("\n--- Parsing RRule from string ---")
	ruleStr := "FREQ=MONTHLY;COUNT=6;BYMONTHDAY=10,20"
	// FREQ=MONTHLY: Tần suất lặp lại là hàng tháng
	// COUNT=6: Giới hạn số lần lặp lại là 6 lần
	// BYMONTHDAY=10,20: Chỉ định ngày trong tháng - ngày 10 và 20 của mỗi tháng
	parsedRule, err := rrule.StrToRRule(ruleStr)
	if err != nil {
		fmt.Println("Error parsing RRule:", err)
		return
	}

	fmt.Println("Parsed RRule String:", parsedRule.String())
	fmt.Println("Setting start date for parsed rule:")
	parsedRule.OrigOptions.Dtstart = time.Date(2023, 1, 1, 9, 0, 0, 0, time.UTC)
	// Khi thiết lập Dtstart, tất cả các ngày lặp lại sẽ được tính từ ngày này. Trong trường hợp này, quy tắc sẽ tạo ra 6 ngày lặp lại vào ngày 10 và 20 của mỗi tháng, bắt đầu từ tháng 1/2023.

	fmt.Println("All occurrences from parsed rule:")
	printTimeSlice(parsedRule.All())

	fmt.Println("\n--- Using After() to find next occurrence ---")
	// Create a schedule with a daily rule
	// FREQ=DAILY;INTERVAL=2: Tần suất lặp lại là hàng ngày và mỗi lần lặp lại là 2 ngày
	schedule := Schedule{
		Config: ScheduleConfig{
			RecurringRule: "FREQ=DAILY;INTERVAL=2",
			StartTime:     time.Date(2023, 5, 1, 10, 0, 0, 0, time.UTC),
		},
	}

	// Get current time for demonstration
	currentTime := time.Date(2023, 5, 5, 15, 30, 0, 0, time.UTC)
	fmt.Println("Current time:", currentTime.Format("2006-01-02 15:04:05 MST"))

	// Find next occurrence
	nextTime, err := schedule.NewTimeToSend(currentTime)
	if err != nil {
		fmt.Println("Error finding next occurrence:", err)
		return
	}

	fmt.Println("Next occurrence:", nextTime.Format("2006-01-02 15:04:05 MST"))

	// Demonstrate inclusive parameter
	fmt.Println("\nDemonstrating After() with inclusive=true:")
	exactTime := time.Date(2023, 5, 7, 10, 0, 0, 0, time.UTC) // An exact occurrence time
	fmt.Println("Exact occurrence time:", exactTime.Format("2006-01-02 15:04:05 MST"))

	// Create a rule directly for demonstration
	r, err := rrule.StrToRRule(schedule.Config.RecurringRule)
	if err != nil {
		fmt.Println("Error creating rule:", err)
		return
	}
	r.OrigOptions.Dtstart = schedule.Config.StartTime

	// Test with inclusive=true and inclusive=false
	nextInclusive := r.After(exactTime, true)
	nextExclusive := r.After(exactTime, false)

	fmt.Println("Next (inclusive=true):", nextInclusive.Format("2006-01-02 15:04:05 MST"))
	fmt.Println("Next (inclusive=false):", nextExclusive.Format("2006-01-02 15:04:05 MST"))
}
